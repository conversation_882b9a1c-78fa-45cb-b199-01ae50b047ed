export default {
    create(data: BorrowingApplicationForm) {
        return request({
            url: '/nebula/api/v1/borrowing-application/create',
            method: 'post',
            data
        });
    },
    update(data: BorrowingApplicationForm) {
        return request({
            url: '/nebula/api/v1/borrowing-application/update',
            method: 'post',
            data
        });
    },
    list(data: BorrowingApplicationListParams) {
        return request({
            url: '/nebula/api/v1/borrowing-application/list',
            method: 'post',
            data
        });
    },
    delete(id: string) {
        return request({
            url: '/nebula/api/v1/borrowing-application/delete',
            method: 'post',
            data: { id }
        });
    }
};

export interface BorrowingApplicationForm {
    id?: string;
    applicant?: string;
    applyDate?: number;
    borrowPeriod?: [number, number] | null;
    borrowReason?: string;
    otherReason?: string;
    fileList?: BorrowingFileItem[];
}

export interface BorrowingFileItem {
    id?: string;
    fileType?: string;
    fileCategory?: string | null;
    fileName?: string;
    fileNumber?: string;
    fileVersion?: string;
    fileId?: string;
    status?: string;
}

export interface BorrowingApplicationListParams {
    applicant?: string;
    applyDate?: number;
    status?: number;
    fileType?: string;
    docCategoryIds?: string[];
    page?: number;
    pageSize?: number;
}

export interface BorrowingApplicationRow {
    id: string;
    applicant?: string;
    applyDate?: number;
    auditors?: string;
    approvers?: string;
    status?: number;
    borrowFileCount?: number;
    returnFileCount?: number;
    borrowPeriod?: string;
    recyclers?: string;
}
